var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(l){var h=0;return function(){return h<l.length?{done:!1,value:l[h++]}:{done:!0}}};$jscomp.arrayIterator=function(l){return{next:$jscomp.arrayIteratorImpl(l)}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;$jscomp.FORCE_POLYFILL_PROMISE=!1;$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(l,h,b){if(l==Array.prototype||l==Object.prototype)return l;l[h]=b.value;return l};$jscomp.getGlobal=function(l){l=["object"==typeof globalThis&&globalThis,l,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var h=0;h<l.length;++h){var b=l[h];if(b&&b.Math==Math)return b}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(l,h){var b=$jscomp.propertyToPolyfillSymbol[h];if(null==b)return l[h];b=l[b];return void 0!==b?b:l[h]};
$jscomp.polyfill=function(l,h,b,q){h&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(l,h,b,q):$jscomp.polyfillUnisolated(l,h,b,q))};$jscomp.polyfillUnisolated=function(l,h,b,q){b=$jscomp.global;l=l.split(".");for(q=0;q<l.length-1;q++){var r=l[q];if(!(r in b))return;b=b[r]}l=l[l.length-1];q=b[l];h=h(q);h!=q&&null!=h&&$jscomp.defineProperty(b,l,{configurable:!0,writable:!0,value:h})};
$jscomp.polyfillIsolated=function(l,h,b,q){var r=l.split(".");l=1===r.length;q=r[0];q=!l&&q in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var d=0;d<r.length-1;d++){var y=r[d];if(!(y in q))return;q=q[y]}r=r[r.length-1];b=$jscomp.IS_SYMBOL_NATIVE&&"es6"===b?q[r]:null;h=h(b);null!=h&&(l?$jscomp.defineProperty($jscomp.polyfills,r,{configurable:!0,writable:!0,value:h}):h!==b&&(void 0===$jscomp.propertyToPolyfillSymbol[r]&&(b=1E9*Math.random()>>>0,$jscomp.propertyToPolyfillSymbol[r]=$jscomp.IS_SYMBOL_NATIVE?
$jscomp.global.Symbol(r):$jscomp.POLYFILL_PREFIX+b+"$"+r),$jscomp.defineProperty(q,$jscomp.propertyToPolyfillSymbol[r],{configurable:!0,writable:!0,value:h})))};$jscomp.initSymbol=function(){};
$jscomp.polyfill("Symbol",function(l){if(l)return l;var h=function(d,y){this.$jscomp$symbol$id_=d;$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:y})};h.prototype.toString=function(){return this.$jscomp$symbol$id_};var b="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",q=0,r=function(d){if(this instanceof r)throw new TypeError("Symbol is not a constructor");return new h(b+(d||"")+"_"+q++,d)};return r},"es6","es3");
$jscomp.polyfill("Symbol.iterator",function(l){if(l)return l;l=Symbol("Symbol.iterator");for(var h="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),b=0;b<h.length;b++){var q=$jscomp.global[h[b]];"function"===typeof q&&"function"!=typeof q.prototype[l]&&$jscomp.defineProperty(q.prototype,l,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return l},"es6",
"es3");$jscomp.iteratorPrototype=function(l){l={next:l};l[Symbol.iterator]=function(){return this};return l};$jscomp.iteratorFromArray=function(l,h){l instanceof String&&(l+="");var b=0,q=!1,r={next:function(){if(!q&&b<l.length){var d=b++;return{value:h(d,l[d]),done:!1}}q=!0;return{done:!0,value:void 0}}};r[Symbol.iterator]=function(){return r};return r};
$jscomp.polyfill("Array.prototype.values",function(l){return l?l:function(){return $jscomp.iteratorFromArray(this,function(h,b){return b})}},"es8","es3");
(function(l,h){"function"===typeof define&&define.amd?define([],h(l)):"object"===typeof exports?module.exports=h(l):l.contactUs=h(l)})("undefined"!==typeof global?global:this.window||this.global,function(l){var h={},b=null,q=!1,r=!!document.querySelector&&!!l.addEventListener,d,y=[],T,L,I=!1,x=!1,C=!1,M=!1,z=!1,N=null,G=null,O=[],P=null,U=null,V=null,na={rootElementId:"contactus",activated:!1,pluginVersion:"2.4.1",wordpressPluginVersion:!1,align:"right",mode:"regular",visible:!0,countdown:0,drag:!1,
online:null,buttonText:"Contact us",buttonSize:"large",buttonIconSize:24,menuSize:"normal",buttonIcon:'<svg width="20" height="20" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g transform="translate(-825 -308)"><g><path transform="translate(825 308)" fill="#FFFFFF" d="M 19 4L 17 4L 17 13L 4 13L 4 15C 4 15.55 4.45 16 5 16L 16 16L 20 20L 20 5C 20 4.45 19.55 4 19 4ZM 15 10L 15 1C 15 0.45 14.55 0 14 0L 1 0C 0.45 0 0 0.45 0 1L 0 15L 4 11L 14 11C 14.55 11 15 10.55 15 10Z"/></g></g></svg>',
reCaptcha:!1,reCaptchaAction:"callbackRequest",reCaptchaKey:"",errorMessage:"Connection error. Please try again.",showMenuHeader:!1,menuHeaderText:"How would you like to contact us?",menuSubheaderText:"",menuHeaderLayout:"icon-center",layout:"default",itemsHeader:"Start chat with:",menuHeaderIcon:null,menuHeaderTextAlign:"center",menuHeaderOnline:!0,showHeaderCloseBtn:!0,menuInAnimationClass:"arcu-show",menuOutAnimationClass:"",headerCloseBtnBgColor:"#787878",headerCloseBtnColor:"#FFFFFF",items:[],
itemsIconType:"rounded",iconsAnimationSpeed:800,iconsAnimationPause:2E3,promptPosition:"side",style:null,itemsAnimation:null,popupAnimation:"scale",forms:{},theme:"#000000",subMenuHeaderBackground:"#FFFFFF",subMenuHeaderColor:"#FFFFFF",closeIcon:'<svg width="12" height="13" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g transform="translate(-4087 108)"><g><path transform="translate(4087 -108)" fill="currentColor" d="M 14 1.41L 12.59 0L 7 5.59L 1.41 0L 0 1.41L 5.59 7L 0 12.59L 1.41 14L 7 8.41L 12.59 14L 14 12.59L 8.41 7L 14 1.41Z"></path></g></g></svg>',
backIcon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512"><path fill="currentColor" d="M231.293 473.899l19.799-19.799c4.686-4.686 4.686-12.284 0-16.971L70.393 256 251.092 74.87c4.686-4.686 4.686-12.284 0-16.971L231.293 38.1c-4.686-4.686-12.284-4.686-16.971 0L4.908 247.515c-4.686 4.686-4.686 12.284 0 16.971L214.322 473.9c4.687 4.686 12.285 4.686 16.971-.001z" class=""></path></svg>',credits:!0,creditsUrl:"https://anychat.one?utm_source=widget",clickAway:!0,backdrop:!1,menuDirection:"vertical",
unreadCount:0,buttonTitle:null,buttonDescription:null,buttonLabel:null,menuStyle:"regular"},Z=function(a,c,e){if("[object Object]"===Object.prototype.toString.call(a))for(var f in a)Object.prototype.hasOwnProperty.call(a,f)&&c.call(e,a[f],f,a);else{f=0;for(var k=a.length;f<k;f++)c.call(e,a[f],f,a)}},oa=function(a,c){var e={};Z(a,function(f,k){e[k]=a[k]});Z(c,function(f,k){e[k]=c[k]});return e},pa=function(){var a=g("div",{classes:["messangers-block","arcuAnimated"]}),c=g("div",{classes:["messangers-list-container"]});
if("personal"==d.layout){var e=g("div",{classes:["arcu-items-header"]},d.itemsHeader),f=g("div",{classes:["arcu-wellcome"]});c.append(f);c.append(e)}e=g("ul",{classes:["messangers-list"]});d.itemsAnimation&&e.classList.add("arcu-"+d.itemsAnimation);"large"===d.menuSize&&a.classList.add("lg");"small"===d.menuSize&&a.classList.add("sm");aa(e,d.items);if(d.showMenuHeader){f=g("div",{classes:["arcu-menu-header","arcu-"+d.menuHeaderLayout],style:d.theme?"background-color:"+d.theme:null});var k=g("div",
{classes:["arcu-menu-header-content","arcu-text-"+d.menuHeaderTextAlign]},d.menuHeaderText);if(d.menuHeaderIcon){var n=g("div",{classes:["arcu-header-icon"]});d.menuHeaderIcon.match(/^https?:\/\//)?(n.style.cssText="background-image: url("+d.menuHeaderIcon+")",n.classList.add("arcu-bg-image")):n.append(t(d.menuHeaderIcon));if(null!==d.menuHeaderOnline){var p=g("div",{classes:["arcu-online-badge",d.menuHeaderOnline?"online":"offline"],style:"border-color: "+d.theme});n.append(p)}f.append(n)}f.append(k);
d.menuSubheaderText&&(k=g("div",{classes:["arcu-menu-subheader","arcu-text-"+d.menuHeaderTextAlign]},d.menuSubheaderText),f.append(k));d.showHeaderCloseBtn&&(k=g("div",{classes:["arcu-header-close"],style:"color:"+d.headerCloseBtnColor+"; background:"+d.headerCloseBtnBgColor}),k.append(t(d.closeIcon)),f.append(k));a.append(f);a.classList.add("has-header")}"rounded"==d.itemsIconType?e.classList.add("rounded-items"):e.classList.add("not-rounded-items");c.append(e);a.append(c);"elastic"==d.style?(e=
t('<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 100 800" preserveAspectRatio="none"><path d="M-1,0h101c0,0-97.833,153.603-97.833,396.167C2.167,627.579,100,800,100,800H-1V0z"/></svg>'),f=g("div",{classes:["arcu-morph-shape"],id:"arcu-morph-shape","data-morph-open":"M-1,0h101c0,0,0-1,0,395c0,404,0,405,0,405H-1V0z"}),f.append(e),a.append(f)):"bubble"==d.style&&(e=t('<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 100 800" preserveAspectRatio="none"><path d="M-7.312,0H0c0,0,0,113.839,0,400c0,264.506,0,400,0,400h-7.312V0z"></path><defs></defs></svg>'),
f=g("div",{classes:["arcu-morph-shape"],id:"arcu-morph-shape","data-morph-open":"M-7.312,0H15c0,0,66,113.339,66,399.5C81,664.006,15,800,15,800H-7.312V0z;M-7.312,0H100c0,0,0,113.839,0,400c0,264.506,0,400,0,400H-7.312V0z"}),f.append(e),a.append(f));d.credits&&(e=g("div",{classes:["arcu-creds"]}),e.innerHTML='powered by <a href="'+d.creditsUrl+'" target="_blank">anychat.one</a>',c.append(e));b.append(a)},qa=function(){var a=g("div",{classes:["arcu-popups-block","arcuAnimated"]}),c=g("div",{classes:["arcu-popups-list-container"]}),
e;for(e in y){var f=y[e];if("object"===typeof f){var k=g("div",{classes:["arcu-popup"],id:"arcu-popup-"+f.id}),n=g("div",{classes:["arcu-popup-header"],style:d.theme?"background-color:"+d.theme:null}),p=g("div",{classes:["arcu-popup-close"],style:d.theme?"background-color:"+d.theme:null}),u=g("div",{classes:["arcu-popup-back"],style:d.theme?"background-color:"+d.theme:null});p.append(t(d.closeIcon));u.append(t(d.backIcon));n.innerHTML=f.title;n.append(p);n.append(u);p=g("div",{classes:["arcu-popup-content"]});
p.innerHTML=f.popupContent;k.append(n);k.append(p);c.append(k)}}a.append(c);b.append(a)},ra=function(){var a=g("div",{classes:["arcu-message-button"],style:A()});"large"===d.buttonSize&&b.classList.add("lg");"huge"===d.buttonSize&&b.classList.add("hg");"medium"===d.buttonSize&&b.classList.add("md");"small"===d.buttonSize&&b.classList.add("sm");if(null!==d.online){var c=g("div",{classes:["arcu-online-badge",!0===d.online?"online":"offline"]});a.append(c)}c=g("div",{classes:["arcu-unread-badge"]});
a.append(c);c=g("div",{classes:["static"]});var e=g("div",{classes:["static-container"]});c.append(e);var f=g("div",{classes:["img-"+d.buttonIconSize]});f.append(t(d.buttonIcon));!1!==d.buttonText?f.append(t("<p>"+d.buttonText+"</p>")):a.classList.add("no-text");e.append(f);e=g("div",{classes:["icons","arcu-hide"]});f=g("div",{classes:["icons-line"]});for(var k in d.items){var n=d.items[k];if("object"===typeof n&&n.includeIconToSlider){var p=g("span",{style:W()});p.append(t(n.icon));f.append(p)}}e.append(f);
k=g("div",{classes:["arcu-close"]});k.append(t(d.closeIcon));f=g("div",{classes:["pulsation"],style:A()});n=g("div",{classes:["pulsation"],style:A()});p=g("div",{classes:["arcu-button-icon"]});p.append(c);p.append(e);p.append(k);a.append(p);a.append(f);a.append(n);D(d.buttonTitle)&&D(d.buttonDescription)&&D(d.buttonLabel)||(c=g("div",{classes:["arcu-button-content"]}),D(d.buttonTitle)||(e=g("div",{classes:["arcu-button-title"]}),e.append(t(d.buttonTitle)),c.append(e)),D(d.buttonDescription)||(e=g("div",
{classes:["arcu-button-descr"]}),e.append(t(d.buttonDescription)),c.append(e)),D(d.buttonLabel)||(e=g("div",{classes:["arcu-button-label"]}),e.append(t(d.buttonLabel)),c.append(e)),a.append(c));b.append(a);ba(d.unreadCount)},aa=function(a,c){for(var e in c){var f=c[e];f.id=f.id?f.id:"arcu-menu-item-"+e;var k=g("li",{});ca(a,k,f);a.append(k)}},sa=function(a,c){if(b.querySelector("#"+a)&&(a=b.querySelector("#"+a).querySelector(".arcu-item-label"),a.querySelector(".arcu-item-labels")&&a.querySelector(".arcu-item-labels").remove(),
c&&0<c.length)){var e=g("div",{classes:["arcu-item-labels"]});c.map(function(f){f=g("span",{classes:["arcu-item-lbl"],style:"background: "+f.background+"; color: "+f.color},f.title);e.append(f)});a.append(e)}},ca=function(a,c,e){if("object"===typeof e){"_popup"==e.href?(y.push(e),a=g("div",{classes:["messanger","arcu-popup-link",e.class?e.class:""],id:e.id?e.id:null,title:e.title,"data-id":e.id?e.id:null})):a=g("a",{classes:["messanger",e.class?e.class:"",e.addons?"has-addon":""],id:e.id?e.id:null,
rel:"nofollow noopener",href:e.href,title:e.title,target:e.target?e.target:"_blank"});e.disabled&&!0===e.disabled&&a.classList.add("arcu-disabled");e.onClick&&a.addEventListener("click",e.onClick);if(e.addons)for(var f in e.addons){var k=e.addons[f],n=g("a",{href:k.href,title:k.title?k.title:null,target:k.target?k.target:"_blank",classes:[k.class?k.class:"arcu-addon"],style:(k.color?"color:"+k.color:null)+"; background-color: transparent"});if(k.icon)if(0===k.icon.indexOf("<"))n.append(t(k.icon));
else if(-1===k.icon.indexOf("<")){var p=g("img",{src:k.icon});n.append(p)}k.onClick&&n.addEventListener("click",k.onClick);a.append(n)}p="rounded"==d.itemsIconType?e.noContainer?g("span",{style:e.color?"color:"+e.color+"; fill: "+e.color:null,classes:["no-container","arcu-item-icon"]}):g("span",{style:e.color&&!e.noContainer?"background-color:"+e.color:null,classes:["arcu-item-icon"]}):e.noContainer?g("span",{style:e.color?"color:"+e.color+"; fill: "+e.color:null,classes:["no-container","arcu-item-icon"]}):
g("span",{style:(e.color&&!e.noContainer?"color:"+e.color:null)+"; background-color: transparent",classes:["arcu-item-icon"]});"undefined"!==typeof e.online&&null!==e.online&&(f=g("div",{classes:["arcu-online-badge",!0===e.online?"online":"offline"]}),p.append(f));p.append(t(e.icon));a.append(p);f=g("div",{classes:["arcu-item-label"]});p=g("div",{classes:["arcu-item-title"]},e.title);f.append(p);"undefined"!=typeof e.subTitle&&e.subTitle&&(p=g("div",{classes:["arcu-item-subtitle"]},e.subTitle),f.append(p));
if(e.labels&&0<e.labels.length){var u=g("div",{classes:["arcu-item-labels"]});e.labels.map(function(v){v=g("span",{classes:["arcu-item-lbl"],style:"background: "+v.background+"; color: "+v.color},v.title);u.append(v)});f.append(u)}a.append(f);c.append(a);if(e.items){var m=e.id;a=g("div",{classes:["arcu-submenu-header"],style:"background-color:"+d.subMenuHeaderBackground+"; color: "+e.subMenuHeaderIconColor});f=g("div",{classes:["arcu-submenu-title","arcu-text-"+e.subMenuHeaderTextAlign],style:"color:"+
d.subMenuHeaderColor});f.innerHTML=e.subMenuHeader?e.subMenuHeader:e.title;p=g("div",{classes:["arcu-submenu-back"],style:"color:"+d.subMenuHeaderColor+"; fill: "+d.subMenuHeaderColor,"data-erl":m},d.backIcon);p.addEventListener("click",function(){da({id:"#"+m})});a.append(p);e.subMenuHeaderIcon&&a.append(t(e.subMenuHeaderIcon));a.append(f);f=g("div",{classes:["arcu-submenu-container"]});p=g("ul",{classes:["arcu-submenu"]});f.append(a);f.append(p);aa(p,e.items);c.append(f)}}},ea=function(a,c){Element.prototype.matches||
(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(f){f=(this.document||this.ownerDocument).querySelectorAll(f);for(var k=f.length;0<=--k&&f.item(k)!==this;);return-1<k});for(var e=[];a&&a!==document;a=a.parentNode)c?a.matches(c)&&e.push(a):e.push(a);return e},da=function(a){b.querySelector(".arcu-submenu-header").classList.add("active");
a=b.querySelector(a.id);a.parentElement.classList.remove("active");a.parentElement.querySelector(".arcu-submenu-container").classList.remove("active");a.parentElement.querySelector(".arcu-submenu-header").classList.add("active");b.querySelectorAll(".arcu-submenu-header").forEach(function(c){c.classList.remove("active")});b.querySelectorAll(".arcu-submenu").forEach(function(c){c.classList.remove("active")});b.querySelector(".arcu-submenu-container.active")?(b.querySelector(".arcu-submenu-container.active > .arcu-submenu-header").classList.add("active"),
b.querySelector(".arcu-submenu-container.active > .arcu-submenu").classList.add("active")):b.querySelector(".messangers-list").classList.remove("arcu-submenu-active")},ta=function(a){b.querySelectorAll(".arcu-submenu-container").forEach(function(f){f.classList.remove("active")});b.querySelectorAll(".arcu-submenu-container .arcu-submenu").forEach(function(f){f.classList.remove("active")});b.querySelectorAll(".messangers-list li").forEach(function(f){f.classList.remove("active")});b.querySelector(".messangers-list").classList.add("arcu-submenu-active");
b.querySelectorAll(".arcu-submenu-header").forEach(function(f){f.classList.remove("active")});var c=b.querySelector(a.id);c.parentElement.querySelector(".arcu-submenu-container").classList.add("active");c.parentElement.querySelector(".arcu-submenu-container").classList.add("animated");c.parentElement.querySelector(".arcu-submenu-container > .arcu-submenu").classList.add("active");setTimeout(function(){c.parentElement.querySelector(".arcu-submenu-container").classList.remove("animated")},300);if(a=
ea(c,".arcu-submenu-container"))for(var e in a)a[e].classList.add("active");if(a=ea(c.parentElement,"li"))for(e in a)a[e].classList.add("active");c.parentElement.classList.add("active");c.parentElement.querySelector(".arcu-submenu-container > .arcu-submenu-header").classList.add("active")},fa=function(a){if(!b.querySelector("#arcu-form-"+a))return console.error("Form not found: "+a),!1;z=!0;E(!1);b.classList.add("open");b.querySelector(".arcu-forms-container").classList.add("active");b.querySelector(".arcu-form-container.active")&&
b.querySelector(".arcu-form-container.active").classList.remove("active");b.querySelector("#arcu-form-"+a).classList.add("active");b.querySelector("#form-icon-"+a)&&(b.querySelector("#form-icon-"+a).classList.add("active"),b.querySelector(".arcu-message-button .static").classList.add("arcu-hide"));!1===d.visible&&h.show();a=new CustomEvent("arcontactus.showForm",{detail:{id:a}});b.dispatchEvent(a)},X=function(){b.querySelector(".arcu-forms-container").classList.remove("active");b.querySelectorAll(".form-icon")&&
b.querySelectorAll(".form-icon").forEach(function(c){c.classList.remove("active")});b.querySelector(".arcu-message-button .static").classList.remove("arcu-hide");z=!1;setTimeout(function(){x||b.classList.remove("open");b.querySelector(".arcu-form-success.active")&&b.querySelector(".arcu-form-success.active").classList.remove("active");b.querySelector(".arcu-form-error.active")&&b.querySelector(".arcu-form-error.active").classList.remove("active");B()},150);!1===d.visible&&h.hide();var a=new Event("arcontactus.hideForm");
b.dispatchEvent(a)},D=function(a){return null===a||!1===a||""===a||"0"===a||0===a},t=function(a){if("string"===typeof a){var c=document.createElement("template");a=a.trim();c.innerHTML=a;return c.content.firstChild}},A=function(a){return"undefined"!==typeof a?"background-color: "+a:"background-color: "+d.theme},W=function(a){return"undefined"!==typeof a?"color: "+a:"color: "+d.theme},g=function(a,c,e){a=document.createElement(a);if(c){if(c.classes&&"object"===typeof c.classes)for(var f in c.classes)c.classes[f]&&
"string"===typeof c.classes[f]&&a.classList.add(c.classes[f]);for(f in c)"classes"!==f&&c[f]&&"string"===typeof c[f]&&a.setAttribute(f,c[f])}"undefined"!==typeof e&&(a.innerHTML=e);return a},ua=function(){var a=g("div",{classes:["arcu-forms-container"]}),c=g("div",{classes:["arcu-form-close"],style:"background-color:"+d.theme+"; color: #FFFFFF"},d.closeIcon);a.append(c);for(var e in d.forms)if(c=d.forms[e],"object"===typeof c){if(c.icon){var f=g("div",{id:"form-icon-"+e,classes:["form-icon"]});f.append(t(c.icon));
var k=b.querySelector(".arcu-button-icon");k&&k.append(f)}f=g("div",{classes:["arcu-form-container"],id:"arcu-form-"+e});k="undefined"!==typeof c.action?g("form",{action:c.action,method:"POST",classese:["arcu-form"],"data-id":e}):g("div",{classes:["arcu-form"]});if("string"==typeof c.header){var n=g("div",{classes:["arcu-form-header"],style:A()},c.header);k.append(n)}else if("object"==typeof c.header){n=g("div",{classes:["arcu-form-header",c.header.layout],style:A()});var p=g("div",{classes:["arcu-form-header-content"]},
c.header.content),u=g("div",{classes:["arcu-form-header-icon"]},c.header.icon);n.append(u);n.append(p);k.append(n)}n=void 0;p=c;u=k;for(n in p.fields){var m=p.fields[n];if("object"===typeof m){var v=g("div",{classes:["arcu-form-group","arcu-form-group-type-"+m.type,"arcu-form-group-"+m.name,m.required?"arcu-form-group-required":""]}),w="input";switch(m.type){case "textarea":w="textarea";break;case "dropdown":w="select"}if(m.label){var F=g("div",{classes:["arcu-form-label"]}),Q=g("label",{for:"arcu-field-"+
p.id+"-"+m.name},m.label);F.append(Q);v.append(F)}w=g(w,{name:m.name,classes:["arcu-form-field","arcu-field-"+m.name],required:m.required,type:"dropdown"==m.type?null:m.type,id:"arcu-field-"+p.id+"-"+m.name,value:m.value?m.value:""});"textarea"==m.type&&m.value&&(w.innerHTML=m.value);m.placeholder&&w.setAttribute("placeholder",m.placeholder);"undefined"!=typeof m.maxlength&&w.setAttribute("maxlength",m.maxlength);if("dropdown"==m.type)for(var J in m.values)F=m.values[J],Q=m.values[J],"object"==typeof m.values[J]&&
(F=m.values[J].value,Q=m.values[J].label),F=g("option",{value:F},Q),w.append(F);v.append(w);v.append(g("div",{classes:["arcu-form-field-errors"]}));u.append(v)}}n=void 0;p=c;u=k;for(n in p.buttons)if(m=p.buttons[n],"object"===typeof m){v=g("div",{classes:["arcu-form-group","arcu-form-button"]});w="";"undefined"!=typeof m.class&&(w=m.class);if(-1!==["button","submit"].indexOf(m.type))var R=g("button",{id:"arcu-button-"+m.id,classes:["arcu-button",w],type:m.type,style:A(m.background)+";"+(m.color?W(m.color):
"")});else"a"==m.type&&(R=g("a",{id:"arcu-button-"+m.id,classes:["arcu-button",w],href:m.href,type:m.type,style:A(m.background)+";"+(m.color?W(m.color):"")}));m.onClick&&R.addEventListener("click",m.onClick);R.innerHTML=m.label;v.append(R);u.append(v)}f.append(k);"string"==typeof c.success&&(k=g("div",{classes:["arcu-form-success"]}),n=g("div",{},c.success),k.append(n),f.append(k));"string"==typeof c.error&&(k=g("div",{classes:["arcu-form-error"]}),c=g("div",{},c.error),k.append(c),f.append(k));a.append(f)}b.append(a)},
va=function(){var a=g("div",{classes:["arcu-prompt","arcu-prompt-"+d.promptPosition]}),c=g("div",{classes:["arcu-prompt-close"],style:A()+"; color: #FFFFFF"});c.append(t(d.closeIcon));var e=g("div",{classes:["arcu-prompt-inner"]});a.append(c);a.append(e);b.append(a)},ha=function(){H();K()},wa=function(){b.querySelector(".arcu-message-button").addEventListener("click",function(c){if("regular"==d.mode)x||C||M||z?(x&&H(),C&&K()):S();else if("single"==d.mode){var e=b.querySelector(".messangers-list li:first-child a");
e.getAttribute("href")||e.click()}else fa("callback");c.preventDefault()});d.clickAway&&document.addEventListener("click",ha);b.addEventListener("click",function(c){c.stopPropagation();if(c.target.classList.contains("arcu-popup-link")||c.target.closest(".arcu-popup-link")){var e=(c.target.classList.contains("arcu-popup-link")?c.target:c.target.closest(".arcu-popup-link")).getAttribute("data-id");ia(e)}(c.target.classList.contains("arcu-header-close")||c.target.closest(".arcu-header-close"))&&H();
(c.target.classList.contains("arcu-popup-close")||c.target.closest(".arcu-popup-close"))&&K();if(c.target.classList.contains("arcu-popup-back")||c.target.closest(".arcu-popup-back"))K(),S()});b.querySelector(".call-back")&&b.querySelector(".call-back").addEventListener("click",function(c){openCallbackPopup()});b.querySelector(".arcu-form-close")&&b.querySelector(".arcu-form-close").addEventListener("click",function(){null!=N&&(clearInterval(N),N=null);X()});b.querySelector(".arcu-prompt-close")&&
b.querySelector(".arcu-prompt-close").addEventListener("click",function(){Y()});var a=b.querySelectorAll(".arcu-form-container form");a&&a.forEach(function(c){c.addEventListener("submit",function(e){e.preventDefault();c.parentElement.classList.add("ar-loading");d.reCaptcha?grecaptcha.execute(d.reCaptchaKey,{action:d.reCaptchaAction}).then(function(f){c.querySelector(".ar-g-token").value=f;ja(c)}):ja(c)})});setTimeout(function(){ka()},500);window.addEventListener("hashchange",function(c){ka()})},ja=
function(a){var c=new CustomEvent("arcontactus.beforeSendFormData",{detail:{form:a}});b.dispatchEvent(c);var e=new XMLHttpRequest;e.onreadystatechange=function(){if(e.readyState==XMLHttpRequest.DONE){if(200==e.status)if(a.parentElement.classList.remove("ar-loading"),la(a),k=JSON.parse(e.responseText),k.success){a.parentElement.querySelector(".arcu-form-success").classList.add("active");a.parentElement.querySelector(".arcu-form-error").classList.remove("active");var n=new CustomEvent("arcontactus.successSendFormData",
{detail:{form:a,data:k}})}else{if(k.errors&&(n=k,0==n.success))for(var p in n.errors)a.querySelector(".arcu-form-group-"+p)&&(a.querySelector(".arcu-form-group-"+p).classList.add("has-error"),a.querySelector(".arcu-form-group-"+p).querySelector(".arcu-form-field-errors").innerHTML=n.errors[p].join("<br/>"));n=new CustomEvent("arcontactus.errorSendFormData",{detail:{form:a,data:k}})}else la(a),a.parentElement.querySelector(".arcu-form-success")&&a.parentElement.querySelector(".arcu-form-success").classList.remove("active"),
a.parentElement.querySelector(".arcu-form-error")&&a.parentElement.querySelector(".arcu-form-error").classList.add("active"),a.parentElement.classList.remove("ar-loading"),alert(d.errorMessage),n=new CustomEvent("arcontactus.errorSendFormData",{detail:{form:a,data:null}});b.dispatchEvent(n)}};c=a.getAttribute("action");var f=a.getAttribute("method"),k=new FormData(a);e.open(f,c,!0);e.send(k)},la=function(a){a.querySelectorAll(".arcu-form-group.has-error").forEach(function(c){c.classList.remove("has-error")})},
ka=function(){switch(window.location.hash){case "#callback-form":case "callback-form":h.showForm("callback");break;case "#callback-form-close":case "callback-form-close":h.hideForm();break;case "#contactus-menu":case "contactus-menu":h.openMenu();break;case "#contactus-menu-close":case "contactus-menu-close":h.closeMenu();break;case "#contactus-hide":case "contactus-hide":h.hide();break;case "#contactus-show":case "contactus-show":h.show()}},ia=function(a){H();b.querySelector("#arcu-popup-"+a).classList.add("arcu-show");
b.querySelector("#arcu-popup-"+a).classList.contains("popup-opened")||(E(!1),b.classList.add("popup-opened"),b.querySelector("#arcu-popup-"+a).classList.add(d.menuInAnimationClass),b.querySelector(".arcu-close").classList.add("arcu-show"),b.querySelector(".static").classList.add("arcu-hide"),b.querySelector(".icons").classList.add("arcu-hide"),b.querySelectorAll(".pulsation").forEach(function(c){c.classList.add("stop")}),C=!0,!1===d.visible&&h.show(),a=new Event("arcontactus.openPopup"),b.dispatchEvent(a))},
K=function(){if(b.querySelector(".arcu-popup.arcu-show")){setTimeout(function(){b.classList.remove("popup-opened")},150);b.querySelector(".arcu-popup.arcu-show").classList.remove(d.menuInAnimationClass);d.menuOutAnimationClass&&b.querySelector(".arcu-popup.arcu-show").classList.add(d.menuOutAnimationClass);setTimeout(function(){b.classList.remove("popup-opened");B()},150);b.querySelector(".arcu-close").classList.remove("arcu-show");b.querySelector(".static").classList.remove("arcu-hide");C=!1;!1===
d.visible&&h.hide();var a=new Event("arcontactus.closePopup");b.dispatchEvent(a)}},S=function(){if("callback"==d.mode)return console.log("Widget in callback mode"),!1;z&&X();if("elastic"==d.style||"bubble"==d.style)document.querySelector("body").classList.add("arcu-show-menu"),document.querySelector("body").classList.add("arcu-menu-"+d.align),document.querySelector("body").classList.add("arcu-pushed");if(!b.querySelector(".messangers-block").classList.contains(d.menuInAnimationClass)){E(!1);b.classList.add("open");
b.querySelector(".messangers-block").classList.add(d.menuInAnimationClass);b.querySelector(".arcu-close").classList.add("arcu-show");b.querySelector(".icons, .static").classList.add("arcu-hide");b.querySelectorAll(".pulsation").forEach(function(e){e.classList.add("stop")});x=!0;!1===d.visible&&h.show();var a=new Event("arcontactus.openMenu");b.dispatchEvent(a)}if("elastic"==d.style)G.animate({path:P},400,mina.easeinout,function(){});else if("bubble"==d.style){var c=function(e){e>V-1||(G.animate({path:O[e]},
0===e?400:500,0===e?mina.easein:mina.elastic,function(){c(e)}),e++)};c(0)}},H=function(){if("callback"==d.mode)return console.log("Widget in callback mode"),!1;if("elastic"==d.style||"bubble"==d.style)document.querySelector("body").classList.remove("arcu-show-menu"),document.querySelector("body").classList.remove("arcu-menu-"+d.align),setTimeout(function(){document.querySelector("body").classList.remove("arcu-pushed")},500);if(b.querySelector(".messangers-block").classList.contains(d.menuInAnimationClass)){setTimeout(function(){z||
b.classList.remove("open")},150);b.querySelector(".messangers-block").classList.remove(d.menuInAnimationClass);d.menuOutAnimationClass&&(b.querySelector(".messangers-block").classList.add(d.menuOutAnimationClass),setTimeout(function(){b.querySelector(".messangers-block").classList.remove(d.menuOutAnimationClass)},1E3));b.querySelector(".arcu-close").classList.remove("arcu-show");b.querySelector(".static").classList.remove("arcu-hide");b.querySelectorAll(".pulsation").forEach(function(c){c.classList.remove("stop")});
x=!1;d.iconsAnimationPause?L=setTimeout(function(){if(M||x||C||z)return!1;B()},d.iconsAnimationPause):B();!1===d.visible&&h.hide();var a=new Event("arcontactus.closeMenu");b.dispatchEvent(a)}"elastic"!=d.style&&"bubble"!=d.style||setTimeout(function(){G.attr("d",U)},300)},ma=function(a){var c=b.querySelector(".arcu-prompt");a&&a.content&&(c.querySelector(".arcu-prompt-inner").innerHTML=a.content);c.classList.add("active");a=new Event("arcontactus.showPrompt");b.dispatchEvent(a)},Y=function(){b.querySelector(".arcu-prompt").classList.remove("active");
var a=new Event("arcontactus.hidePrompt");b.dispatchEvent(a)},B=function(a){if(x||z||I&&!a)return!1;var c=b.querySelector(".icons-line"),e=b.querySelector(".static");if(null===b.querySelector(".icons-line>span:first-child"))return!1;var f=b.querySelector(".icons-line>span:first-child").clientWidth+40;if("huge"===d.buttonSize)var k=2,n=0;"large"===d.buttonSize&&(k=2,n=0);"medium"===d.buttonSize&&(k=4,n=-2);"small"===d.buttonSize&&(k=4,n=-2);var p=b.querySelector(".icons-line").children.length,u=0;
if(0===d.iconsAnimationSpeed)return!1;I=!0;T=setInterval(function(){0===u&&(c.parentElement.classList.remove("arcu-hide"),e.classList.add("arcu-hide"));var m="translate("+-(f*u+k)+"px, "+n+"px)";c.style.cssText="-webkit-transform:"+m+"; -ms-transform: "+m+"transform: "+m;u++;u>p&&(u>p+1&&(d.iconsAnimationPause&&(E(!0),I&&(L=setTimeout(function(){if(M||x||C||z)return!1;B(!0)},d.iconsAnimationPause))),u=0),c.parentElement.classList.add("arcu-hide"),e.classList.remove("arcu-hide"),m="translate("+-k+
"px, "+n+"px)",c.style.cssText="-webkit-transform:"+m+"; -ms-transform: "+m+"transform: "+m)},d.iconsAnimationSpeed)},E=function(a){clearInterval(T);a||(I=!1,clearTimeout(L));a=b.querySelector(".icons-line");var c=b.querySelector(".static");a.parentElement.classList.add("arcu-hide");c.classList.remove("arcu-hide");a.style.cssText="-webkit-transform:translate(-2px, 0px); -ms-transform: translate(-2px, 0px)transform: translate(-2px, 0px)"},ba=function(a){D(a)?(b.querySelector(".arcu-unread-badge").innerHTML=
"",b.querySelector(".arcu-unread-badge").classList.remove("active")):(b.querySelector(".arcu-unread-badge").innerHTML=a,b.querySelector(".arcu-unread-badge").classList.add("active"))};h.destroy=function(){if(q){E(!1);document.removeEventListener("click",ha);b.innerHTML="";b.className="";var a=new Event("arcontactus.destroy");b.dispatchEvent(a);d=null;clearInterval(T);clearTimeout(L);z=M=C=x=I=!1;G=N=null;O=[];V=U=P=null;q=!1}};h.init=function(a){if(r){h.destroy();d=oa(na,a||{});d.forms.dynamic_form=
{header:""};(b=document.getElementById(d.rootElementId))||console.log("Root element not found");b.classList.add("arcu-widget");b.classList.add("arcu-message");b.classList.add("layout-"+d.layout);""!==d.style&&null!==d.style&&b.classList.add("arcu-"+d.style);null!=d.style&&"popup"!=d.style&&""!=d.style&&"no-background"!=d.style||!d.popupAnimation||(b.classList.add("arcu-"+d.popupAnimation),"no-background"==d.style&&b.classList.add("arcu-menu-"+d.menuDirection));d.menuStyle&&null!==d.menuStyle&&b.classList.add("arcu-menu-"+
d.menuStyle);"left"===d.align?b.classList.add("left"):b.classList.add("right");if(d.items.length){if("regular"==d.mode||"single"==d.mode)pa(),"single"==d.mode&&(a=b.querySelector(".messangers-list li:first-child a"),a.getAttribute("href")&&b.append(g("a",{href:a.getAttribute("href"),target:a.getAttribute("target"),classes:["arcu-single-mode-link"]})));y.length&&qa();ra();ua();va();wa();setTimeout(function(){B()},d.iconsAnimationPause?d.iconsAnimationPause:2E3);d.backdrop&&(a=g("div",{classes:["arcu-backdrop"]}),
b.append(a));!0===d.visible&&b.classList.add("active")}else console.info("jquery.contactus:no items");if("elastic"==d.style||"bubble"==d.style)a=document.getElementById("arcu-morph-shape"),G=Snap(a.querySelector("svg")).select("path"),P=a.getAttribute("data-morph-open"),U=G.attr("d"),O=P.split(";"),V=O.length;q=!0;a=new Event("arcontactus.init");b.dispatchEvent(a)}};h.isInitialized=function(){return q};h.getSettings=function(){return d};h.getRootElement=function(){return b};h.openMenu=function(){return S()};
h.closeMenu=function(){return H()};h.toggleMenu=function(){Y();b.querySelector(".messangers-block").classList.contains(d.menuInAnimationClass)?H():S();var a=new Event("arcontactus.toggleMenu");b.dispatchEvent(a)};h.showForm=function(a){return fa(a)};h.hideForm=function(){return X()};h.showPromptTyping=function(){b.querySelector(".arcu-prompt").querySelector(".arcu-prompt-inner").innerHTML="";var a=b.querySelector(".arcu-prompt-inner"),c=g("div",{classes:["arcu-prompt-typing"]}),e=g("div");c.append(e);
c.append(e.cloneNode());c.append(e.cloneNode());a.append(c);ma({});a=new Event("arcontactus.showPromptTyping");b.dispatchEvent(a)};h.hidePromptTyping=function(){b.querySelector(".arcu-prompt").classList.remove("active");var a=new Event("arcontactus.hidePromptTyping");b.dispatchEvent(a)};h.showPrompt=function(a){return ma(a)};h.hidePrompt=function(){return Y()};h.showWellcomeTyping=function(){var a;if(a=b.querySelector(".arcu-wellcome")){var c=b.querySelector(".arcu-menu-header > .arcu-header-icon");
if(!a.querySelector(".arcu-wellcome-msg.typing")){var e=g("div",{classes:["arcu-wellcome-msg","typing"]}),f=g("div",{classes:["arcu-wellcome-icon"]});f.append(c.cloneNode(!0));c=g("div",{classes:["arcu-wellcome-time"]});var k=new Date;c.innerHTML=("0"+k.getHours()).slice(-2)+":"+("0"+k.getMinutes()).slice(-2);k=g("div",{classes:["arcu-wellcome-content"]});var n=g("div",{classes:["arcu-prompt-typing"]}),p=g("div");n.append(p);n.append(p.cloneNode());n.append(p.cloneNode());k.append(n);e.append(c);
e.append(f);e.append(k);a.append(e)}a=void 0}else a=!1;return a};h.showWellcomeMessage=function(a){var c=b.querySelector(".arcu-wellcome");c?(c.querySelector(".arcu-wellcome-msg.typing")&&(c.querySelector(".arcu-wellcome-msg.typing .arcu-wellcome-content").innerHTML=a.content,c.querySelector(".arcu-wellcome-msg.typing").classList.remove("typing")),a=void 0):a=!1;return a};h.openPopup=function(a){return ia(a)};h.closePopup=function(){return K()};h.showSubmenu=function(a){return ta(a)};h.hideSubmenu=
function(a){return da(a)};h.show=function(){b.classList.add("active");var a=new Event("arcontactus.show");B();b.dispatchEvent(a)};h.hide=function(){b.classList.remove("active");var a=new Event("arcontactus.hide");E(!1);b.dispatchEvent(a)};h.startAnimation=function(){return B()};h.stopAnimation=function(a){return E(a)};h.triggerItem=function(a,c,e){b.querySelector("#msg-item-"+c)&&(a=new CustomEvent(a,{detail:e}),b.querySelector("#msg-item-"+c).dispatchEvent(a))};h.updateMenuItem=function(a,c){if(b.querySelector("#"+
a)){c.id=a;a=b.querySelector("#"+a).parentElement;a.innerHTML="";var e=b.querySelector("messangers-list");ca(e,a,c)}};h.updateMenuItemStatus=function(a,c){if(b.querySelector("#"+a))if(a=b.querySelector("#"+a),null===c)a.querySelector(".arcu-online-badge")&&a.querySelector(".arcu-online-badge").remove();else if(!1===c||!0===c)a.querySelector(".arcu-online-badge")?(a.querySelector(".arcu-online-badge").classList.remove("online"),a.querySelector(".arcu-online-badge").classList.remove("offline"),a.querySelector(".arcu-online-badge").classList.add(!0===
c?"online":"offline")):(c=g("div",{classes:["arcu-online-badge",!0===c?"online":"offline"]}),a.querySelector(".arcu-item-icon").append(c))};h.setMenuItemDisabled=function(a,c){b.querySelector("#"+a)&&(b.querySelector("#"+a),!0===c?b.querySelector("#"+a).classList.add("arcu-disabled"):b.querySelector("#"+a).classList.remove("arcu-disabled"))};h.setMenuItemLabels=function(a,c){return sa(a,c)};h.isMenuOpened=function(){return x};h.setUnreadCount=function(a){return ba(a)};h.utils={};h.utils.createElement=
function(a,c,e){return g(a,c,e)};h.utils.DOMElementFromHTML=function(a){return t(a)};return h});
