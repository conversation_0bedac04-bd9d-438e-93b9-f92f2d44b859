.arcontactus-widget {
  opacity: 0;
  transition: 0.2s opacity;
}
.arcontactus-widget * {
  box-sizing: border-box;
}
.arcontactus-widget.left.arcontactus-message {
  left: 20px;
  right: auto;
}
.arcontactus-widget.left .messangers-block {
  right: auto;
  left: 0;
  -webkit-transform-origin: 10% 105%;
  -ms-transform-origin: 10% 105%;
  transform-origin: 10% 105%;
}
.arcontactus-widget.left .callback-countdown-block {
  left: 0;
  right: auto;
}
.arcontactus-widget.left .messangers-block::before,
.arcontactus-widget.left .callback-countdown-block::before {
  left: 25px;
  right: auto;
}
.arcontactus-widget.active {
  opacity: 1;
}
.arcontactus-widget.arcontactus-message {
  z-index: 10000;
  right: 20px;
  bottom: 20px;
  position: fixed!important;
  height: 70px;
  width: 70px;
}
.arcontactus-widget .arcontactus-message-button {
  width: 70px;
  position: absolute;
  height: 70px;
  background-color: red;
  border-radius: 50px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
}
.arcontactus-widget .arcontactus-message-button p {
  font-family: Ubuntu, Arial, sans-serif;
  color: #fff;
  font-weight: 700;
  font-size: 10px;
  line-height: 11px;
  margin: 0;
}
.arcontactus-widget .arcontactus-message-button .pulsation {
  width: 84px;
  height: 84px;
  background-color: red;
  border-radius: 50px;
  position: absolute;
  left: -7px;
  top: -7px;
  z-index: -1;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  -webkit-animation: arcontactus-pulse 2s infinite;
  animation: arcontactus-pulse 2s infinite;
}
.arcontactus-widget .arcontactus-message-button .icons {
  background-color: #fff;
  width: 44px;
  height: 44px;
  border-radius: 50px;
  position: absolute;
  overflow: hidden;
  /*-webkit-animation:arcontactus-show-icons 6s infinite;
            animation:arcontactus-show-icons 6s infinite;*/
  top: 50%;
  left: 50%;
  margin-top: -22px;
  margin-left: -22px;
}
.arcontactus-widget .arcontactus-message-button .static {
  position: absolute;
  /*-webkit-animation:arcontactus-show-stat 6s infinite;
            animation:arcontactus-show-stat 6s infinite;*/
  top: 50%;
  left: 50%;
  margin-top: -19px;
  margin-left: -26px;
  width: 52px;
  height: 52px;
  text-align: center;
}
.arcontactus-widget .arcontactus-message-button .static img {
  display: inline;
}
.arcontactus-widget .pulsation:nth-of-type(2n) {
  -webkit-animation-delay: .5s;
  animation-delay: 0.5s;
}
.arcontactus-widget .pulsation.stop {
  -webkit-animation: none;
  animation: none;
}
.arcontactus-widget .icons-line {
  top: 10px;
  left: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: absolute;
  /*-webkit-animation:arcontactus-icon-change 6s infinite;
        animation:arcontactus-icon-change 6s infinite;*/
  -webkit-transition: cubic-bezier(0.13, 1.49, 0.14, -0.4);
  -o-transition: cubic-bezier(0.13, 1.49, 0.14, -0.4);
  transition: cubic-bezier(0.13, 1.49, 0.14, -0.4);
  -webkit-animation-delay: 0s;
  animation-delay: 0s;
  -webkit-transform: translateX(30px);
  -ms-transform: translateX(30px);
  transform: translateX(30px);
  height: 24px;
  transition: 0.2s all;
}
.arcontactus-widget .icons-line.stop {
  -webkit-animation-play-state: paused;
  animation-play-state: paused;
}
.arcontactus-widget .icons-line span {
  display: inline-block;
  width: 24px;
  height: 24px;
  color: red;
}
.arcontactus-widget .icons-line span svg {
  width: 24px;
  height: 24px;
}
.arcontactus-widget .icons-line img,
.arcontactus-widget .icons-line span {
  margin-right: 40px;
}
.arcontactus-widget .static {
  transition: 0.2s all;
}
.arcontactus-widget .static.hide {
  transform: scale(0);
  opacity: 0;
}
.arcontactus-widget .icons {
  transition: 0.2s all;
}
.arcontactus-widget .icons.hide {
  transform: scale(0);
  opacity: 0;
}
.arcontactus-widget .icons.hide .icons-line {
  transform: scale(0);
}
.arcontactus-widget .icons .icon:first-of-type {
  margin-left: 0;
}
.arcontactus-widget .arcontactus-close {
  color: #FFFFFF;
}
.arcontactus-widget .arcontactus-close svg {
  -webkit-transform: rotate(180deg) scale(0);
  -ms-transform: rotate(180deg) scale(0);
  transform: rotate(180deg) scale(0);
  -webkit-transition: ease-in 0.12s all;
  -o-transition: ease-in 0.12s all;
  transition: ease-in 0.12s all;
  display: block;
}
.arcontactus-widget .arcontactus-close.show-messageners-block svg {
  -webkit-transform: rotate(0) scale(1);
  -ms-transform: rotate(0) scale(1);
  transform: rotate(0) scale(1);
}
.arcontactus-widget .messangers-block {
  background: #FFFFFF;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.6);
  width: 235px;
  background-repeat: no-repeat;
  background-position: center;
  position: absolute;
  bottom: 80px;
  right: 0px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  padding: 14px 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 7px;
  -webkit-transform-origin: 80% 105%;
  -ms-transform-origin: 80% 105%;
  transform-origin: 80% 105%;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  -webkit-transition: ease-out 0.12s all;
  -o-transition: ease-out 0.12s all;
  transition: ease-out 0.12s all;
  z-index: 10000;
}
.arcontactus-widget .messangers-block:before {
  position: absolute;
  bottom: -7px;
  right: 25px;
  left: auto;
  display: inline-block !important;
  border-right: 8px solid transparent;
  border-top: 8px solid #FFFFFF;
  border-left: 8px solid transparent;
  content: '';
}
.arcontactus-widget .messangers-block.show-messageners-block {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}
.arcontactus-widget .messanger {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 0;
  cursor: pointer;
  width: 100%;
  padding: 8px 20px 8px 60px;
  position: relative;
  min-height: 54px;
  text-decoration: none;
}
.arcontactus-widget .messanger:hover {
  background-color: #EEEEEE;
}
.arcontactus-widget .messanger:before {
  background-repeat: no-repeat;
  background-position: center;
}
.arcontactus-widget .messanger.facebook span {
  background: #0084ff;
}
.arcontactus-widget .messanger.viber span {
  background: #7c529d;
}
.arcontactus-widget .messanger.telegram span {
  background: #2ca5e0;
}
.arcontactus-widget .messanger.skype span {
  background: #31c4ed;
}
.arcontactus-widget .messanger.email span {
  background: #ff8400;
}
.arcontactus-widget .messanger.contact span {
  background: #7eb105;
}
.arcontactus-widget .messanger.call-back span {
  background: #54cd81;
}
.arcontactus-widget .messanger span {
  position: absolute;
  left: 10px;
  top: 50%;
  margin-top: -20px;
  display: block;
  width: 40px;
  height: 40px;
  border-radius: 50px;
  background-color: #0084ff;
  margin-right: 10px;
  color: #FFFFFF;
  text-align: center;
  vertical-align: middle;
}
.arcontactus-widget .messanger span svg {
  width: 24px;
  height: 24px;
  vertical-align: middle;
  text-align: center;
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -12px;
  margin-left: -12px;
}
.arcontactus-widget .messanger p {
  margin: 0;
  font-family: Arial, sans-serif;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.87);
}
.arcontactus-widget .callback-countdown-block {
  background: #FFFFFF;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.6);
  width: 410px;
  height: 150px;
  background-repeat: no-repeat;
  background-position: center;
  position: absolute;
  bottom: 80px;
  left: auto;
  right: 0;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 7px;
  -webkit-transform-origin: 80% 105%;
  -ms-transform-origin: 80% 105%;
  transform-origin: 80% 105%;
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  -webkit-transition: ease-out 0.12s all;
  -o-transition: ease-out 0.12s all;
  transition: ease-out 0.12s all;
  z-index: 10000;
  color: red;
  padding-top: 5px;
  padding-left: 8px;
  padding-right: 19px;
  display: none;
}
.arcontactus-widget .callback-countdown-block .callback-countdown-block-timer_timer {
  font-size: 38px;
  font-family: Arial, sans-serif;
}
.arcontactus-widget .callback-countdown-block.display-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.arcontactus-widget .callback-countdown-block:before {
  position: absolute;
  bottom: -7px;
  right: 25px;
  left: auto;
  display: inline-block !important;
  border-right: 8px solid transparent;
  border-top: 8px solid #FFFFFF;
  border-left: 8px solid transparent;
  content: '';
}
.arcontactus-widget .callback-countdown-block .callback-countdown-block-phone {
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 16px;
  padding-left: 10px;
  padding-right: 10px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 140px;
  display: none;
  position: relative;
}
.arcontactus-widget .callback-countdown-block .callback-countdown-block-phone:before {
  transition: 0.2s all;
}
.arcontactus-widget .callback-countdown-block .callback-countdown-block-phone.ar-loading:before {
  background: rgba(255, 255, 255, 0.8);
  content: ' ';
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 1;
}
.arcontactus-widget .callback-countdown-block .callback-countdown-block-phone.ar-loading:after {
  content: ' ';
  position: absolute;
  width: 30px;
  height: 30px;
  z-index: 2;
  top: 50%;
  bottom: 50%;
  margin-top: -15px;
  margin-bottom: -15px;
  background: url('../img/ring-alt.gif') no-repeat transparent scroll 0 0;
}
.arcontactus-widget .callback-countdown-block .callback-countdown-block-phone p {
  text-align: center;
  margin-bottom: 10px;
  margin-top: 3px;
}
.arcontactus-widget .callback-countdown-block .callback-countdown-block-phone .callback-countdown-block-form-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.arcontactus-widget .callback-countdown-block .callback-countdown-block-phone .callback-countdown-block-form-group input[type=tel] {
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 16px;
  border-radius: 4px;
  border: 0;
  width: 203px;
  height: 36px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 10px 11px 9px;
  border: 1px solid #DDDDDD;
}
.arcontactus-widget .callback-countdown-block .callback-countdown-block-phone .callback-countdown-block-form-group input[type=submit] {
  border-radius: 4px;
  border: 0;
  background-color: red;
  color: #fff;
  font-family: Arial, sans-serif;
  font-size: 14px;
  padding: 10px 5px 9px;
  cursor: pointer;
  width: 132px;
  height: 36px;
  margin-left: 5px;
}
.arcontactus-widget .callback-countdown-block .callback-countdown-block-phone .callback-countdown-block-form-group input[type=submit]:hover {
  opacity: 0.8;
}
.arcontactus-widget .callback-countdown-block .callback-countdown-block-sorry {
  height: 140px;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: none;
}
.arcontactus-widget .callback-countdown-block .callback-countdown-block-sorry p {
  font-family: Arial, sans-serif;
  font-size: 16px;
  line-height: 18px;
  text-align: center;
  margin-bottom: 5px;
  margin-top: 7px;
}
.arcontactus-widget .callback-countdown-block .callback-countdown-block-close {
  position: absolute;
  top: 9px;
  right: 9px;
  cursor: pointer;
  z-index: 1;
}
.arcontactus-widget .callback-countdown-block .callback-countdown-block-timer {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 140px;
  display: none;
}
.arcontactus-widget .callback-countdown-block .callback-countdown-block-timer p {
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 16px;
  text-align: center;
  margin-bottom: 5px;
  margin-top: 7px;
}
.arcontactus-widget .callback-countdown-block .callback-countdown-block-timer h1 {
  font-family: Ubuntu, Arial, sans-serif;
  font-size: 40px;
  line-height: 46px;
  text-align: center;
  font-weight: 300;
}
.arcontactus-widget .callback-countdown-block .callback-countdown-block-phone.display-flex,
.arcontactus-widget .callback-countdown-block .callback-countdown-block-timer.display-flex,
.arcontactus-widget .callback-countdown-block .callback-countdown-block-sorry.display-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.arcontactus-widget .callback-state {
  background-color: #fff;
  width: 44px;
  height: 44px;
  border-radius: 50px;
  position: absolute;
  overflow: hidden;
  /*-webkit-animation:arcontactus-show-icons 6s infinite;
        animation:arcontactus-show-icons 6s infinite;*/
  top: 50%;
  left: 50%;
  margin-top: -22px;
  margin-left: -22px;
  display: none;
  -webkit-animation: none;
  animation: none;
  z-index: 999999;
  transition: 0.2s all;
  transform: scale(0);
  color: red;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.arcontactus-widget .callback-state.display-flex {
  transform: scale(1);
}
.arcontactus-widget .callback-state svg {
  width: 24px;
  height: 24px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -12px;
  margin-left: -12px;
}
.arcontactus-widget .callback-state .callback-state-img {
  position: absolute;
  top: 12px;
  left: 12px;
}
.arcontactus-widget .animation-pause {
  -webkit-animation-play-state: paused;
  animation-play-state: paused;
}
@-webkit-keyframes arcontactus-pulse {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0;
  }
}
@media (max-width: 468px) {
  .arcontactus-widget.opened.left.arcontactus-message {
    width: auto;
    right: 20px;
    left: 20px;
  }
  .arcontactus-widget.opened.arcontactus-message {
    width: auto;
    right: 20px;
    left: 20px;
  }
  .arcontactus-widget .callback-countdown-block {
    width: 100%;
  }
}
@media (max-width: 428px) {
  .arcontactus-widget .callback-countdown-block .callback-countdown-block-phone .callback-countdown-block-form-group {
    display: block;
  }
  .arcontactus-widget .callback-countdown-block .callback-countdown-block-phone .callback-countdown-block-form-group input[type=tel],
  .arcontactus-widget .callback-countdown-block .callback-countdown-block-phone .callback-countdown-block-form-group input[type=submit] {
    display: block;
    width: 100%;
    margin: 0 0 5px 0;
  }
}
@keyframes arcontactus-pulse {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0;
  }
}
@-webkit-keyframes arcontactus-show-stat {
  0%,
  20% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  21%,
  84% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  85%,
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@keyframes arcontactus-show-stat {
  0%,
  20% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  21%,
  84% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  85%,
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@-webkit-keyframes arcontactus-show-icons {
  0%,
  20% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  21%,
  84% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  85%,
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
}
@keyframes arcontactus-show-icons {
  0%,
  20% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  21%,
  84% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  85%,
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
}
/*# sourceMappingURL=jquery.contactus.css.map */