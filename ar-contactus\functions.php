<?php
if (!function_exists('add_action' )) {
    die('Hi there!  I\'m just a plugin, not much I can do when called directly.');
}

if (!function_exists('wp_removable_query_args')){
    /**
    * Returns an array of single-use query variable names that can be removed from a URL.
    *
    * @since 4.4.0
    *
    * @return array An array of parameters to remove from the URL.
    */
   function wp_removable_query_args() {
        $removable_query_args = array(
                'activate',
                'activated',
                'approved',
                'deactivate',
                'deleted',
                'disabled',
                'enabled',
                'error',
                'hotkeys_highlight_first',
                'hotkeys_highlight_last',
                'locked',
                'message',
                'same',
                'saved',
                'settings-updated',
                'skipped',
                'spammed',
                'trashed',
                'unspammed',
                'untrashed',
                'update',
                'updated',
                'wp-post-new-reload',
        );

        /**
         * Filters the list of query variables to remove.
         *
         * @since 4.2.0
         *
         * @param array $removable_query_args An array of query variables to remove from a URL.
         */
        return apply_filters( 'removable_query_args', $removable_query_args );
    }
}
